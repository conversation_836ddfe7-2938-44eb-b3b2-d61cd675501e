<?xml version="1.0"?>
<package format="2">
  <name>wild_visual_navigation_anymal</name>
  <version>0.0.1</version>
  <description>ANYmal interfaces for wild_visual_navigation</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="j<PERSON><PERSON>@ethz.ch"><PERSON></maintainer>
  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="jon<PERSON>@ethz.ch"><PERSON></author>

  <license>Proprietary</license>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>rospy</depend>
  <depend>roscpp</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>wild_visual_navigation_msgs</depend>
  <depend>wild_visual_navigation_ros</depend>
</package>
