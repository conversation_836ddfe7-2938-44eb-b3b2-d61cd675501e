# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "".split(';') if "" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "wild_visual_navigation_jackal"
PROJECT_SPACE_DIR = "/code/wvm_ws/devel/.private/wild_visual_navigation_jackal"
PROJECT_VERSION = "0.0.1"
