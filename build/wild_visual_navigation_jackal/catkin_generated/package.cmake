set(_CATKIN_CURRENT_PACKAGE "wild_visual_navigation_jackal")
set(wild_visual_navigation_jackal_VERSION "0.0.1")
set(wild_visual_navigation_jackal_MAINTAINER "<PERSON><PERSON> <<EMAIL>>, <PERSON> <j<PERSON><PERSON>@ethz.ch>")
set(wild_visual_navigation_jackal_PACKAGE_FORMAT "2")
set(wild_visual_navigation_jackal_BUILD_DEPENDS "rospy" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_jackal_BUILD_EXPORT_DEPENDS "rospy" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_jackal_BUILDTOOL_DEPENDS "catkin")
set(wild_visual_navigation_jackal_BUILDTOOL_EXPORT_DEPENDS )
set(wild_visual_navigation_jackal_EXEC_DEPENDS "gazebo_ros" "rospy" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_jackal_RUN_DEPENDS "gazebo_ros" "rospy" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_jackal_TEST_DEPENDS )
set(wild_visual_navigation_jackal_DOC_DEPENDS )
set(wild_visual_navigation_jackal_URL_WEBSITE "")
set(wild_visual_navigation_jackal_URL_BUGTRACKER "")
set(wild_visual_navigation_jackal_URL_REPOSITORY "")
set(wild_visual_navigation_jackal_DEPRECATED "")