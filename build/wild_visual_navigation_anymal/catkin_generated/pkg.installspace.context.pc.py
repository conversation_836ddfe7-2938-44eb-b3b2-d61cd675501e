# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "".split(';') if "" != "" else []
PROJECT_CATKIN_DEPENDS = "rospy;roscpp;sensor_msgs;std_msgs;wild_visual_navigation_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "wild_visual_navigation_anymal"
PROJECT_SPACE_DIR = "/code/wvm_ws/install"
PROJECT_VERSION = "0.0.1"
