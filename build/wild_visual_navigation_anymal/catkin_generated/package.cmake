set(_CATKIN_CURRENT_PACKAGE "wild_visual_navigation_anymal")
set(wild_visual_navigation_anymal_VERSION "0.0.1")
set(wild_visual_navigation_anymal_MAINTAINER "<PERSON><PERSON> <<EMAIL>>, <PERSON> <j<PERSON><PERSON>@ethz.ch>")
set(wild_visual_navigation_anymal_PACKAGE_FORMAT "2")
set(wild_visual_navigation_anymal_BUILD_DEPENDS "rospy" "roscpp" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_anymal_BUILD_EXPORT_DEPENDS "rospy" "roscpp" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_anymal_BUILDTOOL_DEPENDS "catkin")
set(wild_visual_navigation_anymal_BUILDTOOL_EXPORT_DEPENDS )
set(wild_visual_navigation_anymal_EXEC_DEPENDS "rospy" "roscpp" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_anymal_RUN_DEPENDS "rospy" "roscpp" "nav_msgs" "sensor_msgs" "std_msgs" "wild_visual_navigation_msgs" "wild_visual_navigation_ros")
set(wild_visual_navigation_anymal_TEST_DEPENDS )
set(wild_visual_navigation_anymal_DOC_DEPENDS )
set(wild_visual_navigation_anymal_URL_WEBSITE "")
set(wild_visual_navigation_anymal_URL_BUGTRACKER "")
set(wild_visual_navigation_anymal_URL_REPOSITORY "")
set(wild_visual_navigation_anymal_DEPRECATED "")