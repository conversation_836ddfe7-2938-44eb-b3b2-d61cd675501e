#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/code/wvm_ws/devel/.private/catkin_tools_prebuild:$CMAKE_PREFIX_PATH"
export PATH='/opt/ros/noetic/bin:/root/env/bin:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
export PWD='/code/wvm_ws/build/catkin_tools_prebuild'
export ROSLISP_PACKAGE_DIRECTORIES='/code/wvm_ws/devel/.private/catkin_tools_prebuild/share/common-lisp'
export ROS_PACKAGE_PATH="/code/wvm_ws/build/catkin_tools_prebuild:$ROS_PACKAGE_PATH"